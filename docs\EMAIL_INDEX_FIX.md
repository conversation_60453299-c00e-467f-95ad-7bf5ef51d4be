# Email Index Duplicate Key Error Fix

## Problem Description

When attempting to login with a mobile number, users were encountering the following error:

```
E11000 duplicate key error collection: HEALTETHER-API.users index: email_1 dup key: { email: "" }
```

This error occurred because:

1. The `email` field in the User model had a default value of empty string (`""`)
2. There was a unique index on the `email` field in the database
3. When creating users with mobile numbers (no email), multiple users would have the same empty string email value
4. MongoDB's unique constraint was violated when trying to insert a second user with an empty email

## Root Cause

The issue was in the User schema definition in `models/user.model.js`:

```javascript
email: {
  type: String,
  default: ""  // This caused the problem
},
```

Combined with a unique index on the email field that didn't allow multiple empty strings.

## Solution Implemented

### 1. Updated User Model Schema

**File:** `models/user.model.js`

- Removed the default value for the `email` field
- Added sparse unique indexes for both `email` and `mobile` fields

```javascript
email: {
  type: String,
  required: false  // No default value
},

// Create indexes for the User schema
userSchema.index({ email: 1 }, { unique: true, sparse: true });
userSchema.index({ mobile: 1 }, { unique: true, sparse: true });
```

### 2. Database Migration

**File:** `scripts/fixEmailIndexMigration2.js`

The migration script:
1. Dropped all existing email-related indexes
2. Removed the `email` field entirely from users who had empty/null emails
3. Created a new sparse unique index on the email field

Key changes:
- Users without emails now have no `email` field at all (undefined)
- Users with emails have unique email addresses
- The sparse index allows unlimited users without the email field

### 3. User Creation Logic

**File:** `user/utils/userUtils.js`

The `createUserWithMobile` function now creates users without an email field:

```javascript
const createUserWithMobile = async (mobile) => {
  console.log(`Created new user with mobile ${mobile}`);
  return await User.create({
    mobile,
    profiles: [],
    abhaAddresses: []
    // No email field - will be undefined
  });
};
```

## Results

After implementing the fix:

✅ **Multiple users can be created with mobile numbers** - No more email duplicate key errors
✅ **Email uniqueness is preserved** - Users with emails still have unique email addresses  
✅ **Backward compatibility** - Existing users with emails are unaffected
✅ **Clean data model** - Users without emails don't have unnecessary empty email fields

## Testing

The fix was thoroughly tested with:

1. **Multiple mobile user creation** - Successfully created multiple users with different mobile numbers
2. **Original error scenario** - The exact mobile number from the error (`9834033728`) now works
3. **Email functionality** - Users with emails can still be created and email uniqueness is enforced
4. **Mixed scenarios** - Users with both mobile and email work correctly

## Files Modified

1. `models/user.model.js` - Updated schema and indexes
2. `scripts/fixEmailIndexMigration2.js` - Database migration script
3. `scripts/testMobileLogin.js` - Test script to verify the fix
4. `scripts/finalTest.js` - Comprehensive test suite

## Migration Steps Performed

1. **Backup recommended** - Always backup your database before running migrations
2. **Run migration script**: `node scripts/fixEmailIndexMigration2.js`
3. **Verify with tests**: `node scripts/finalTest.js`

## Notes

- The fix uses MongoDB's sparse indexes, which ignore documents that don't have the indexed field
- Users created with mobile numbers will have `email: undefined` instead of `email: ""`
- This approach is more efficient and cleaner than using null values
- The mobile field also uses a sparse unique index for consistency

## Future Considerations

- Consider adding validation to ensure either mobile or email is provided when creating users
- Monitor for any other fields that might have similar issues with empty string defaults and unique constraints

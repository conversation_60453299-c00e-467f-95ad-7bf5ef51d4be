const mongoose = require("mongoose");
const { Schema } = mongoose;

// User Schema
const userSchema = new Schema(
  {
    mobile: {
      type: String,
      required: false
    },
    mobileVerified: {
      type: String,
      default: ""
    },
    email: {
      type: String,
      required: false
    },
    emailVerified: {
      type: String,
      default: ""
    },
    abhaLinkedCount: {
      type: String,
      default: null
    },
    address: {
      line: { type: String, default: "" },
      districtName: { type: String, default: "" },
      stateName: { type: String, default: "" },
      pinCode: { type: String, default: "" },
      districtCode: { type: String, default: "" },
      stateCode: { type: String, default: "" },
      townName: { type: String, default: "" }
    },
    switchProfileEnabled: {
      type: Boolean,
      default: true
    },
    preferredAbhaAddress: {
      type: String,
      default: null,
    },
    profiles: [
      {
        _Id: {type: String, required: true},
        firstName: { type: String, required: false },
        lastName: { type: String, required: false },
        middleName: { type: String, default: "" },
        fullName: { type: String, default: "" },
        age: { type: Number, required: false },
        dob: { type: Date, required: false },
        dateOfBirth: { type: String, default: "" }, // Formatted date string (DD-MM-YYYY)
        dayOfBirth: { type: String, default: "" },
        monthOfBirth: { type: String, default: "" },
        yearOfBirth: { type: String, default: "" },
        gender: { type: String, required: false },
        relation: { type: String, required: false },
        abhaId: { type: String, default: "" },
        abhaNumber: { type: String, default: "" }, // 14-digit ABHA number
        abhaAddress: { type: String, default: "" }, // username@abdm format
        abhaCardUrl: { type: String, default: "" }, // URL to stored ABHA card
        kycStatus: { type: String, default: "" }, // Changed from Boolean to String to store "VERIFIED" etc.
        kycMethod: { type: String, enum: ['AADHAAR_OTP', 'AADHAAR_BIOMETRIC', 'AADHAAR_DEMOGRAPHIC', 'DRIVING_LICENSE', null], default: null },
        authMethods: { type: [String], default: [] }, // Available authentication methods
        profilePhoto: { type: String, default: "" },
        profilePhotoFile: { type: String, default: "" }, // File path for profile photo
        status: { type: String, default: "ACTIVE" },
        isMain: { type: Boolean, default: false },
        createdAt: { type: Date, default: Date.now },
        updatedAt: { type: Date, default: Date.now }
      },
    ],
    abhaAddresses: [
      {
        abhaAddress: { type: String, required: false }, // username@abdm format
        abhaNumber: { type: String, required: false }, // 14-digit ABHA number
        isMain: { type: Boolean, default: false },
        createdAt: { type: Date, default: Date.now }
      }
    ],
    tokens: {
      abhaAddress: { type: String, required: false },
      abhaNumber: { type: String, required: false },
      // Local tokens
      refreshToken: { type: String, required: false },
      accessToken: { type: String, required: false },
      // ABDM API tokens
      abhaToken: { type: String, default: null },
      abhaRefreshToken: { type: String, default: null },
      // ABDM token expiry times
      abhaExpiresIn: { type: Number, default: null },
      abhaRefreshExpiresIn: { type: Number, default: null },
      ipAddress: { type: String, required: false },
      userAgent: { type: String, required: false },
      updatedAt: { type: Date, default: Date.now }
    }
  },
  {
    timestamps: true,
  }
);

// Create indexes for the User schema
userSchema.index({ email: 1 }, { unique: true, sparse: true });
userSchema.index({ mobile: 1 }, { unique: true, sparse: true });

// Family Member Schema
const familyMemberSchema = new Schema(
  {
    relation: {
      type: String,
      required: false,
    },
    firstName: {
      type: String,
      required: false,
    },
    lastName: {
      type: String,
      required: false,
    },
    fullName: {
      type: String,
      required: false,
    },
    birthdate: {
      type: Date,
    },
    age: {
      type: Number,
      required: false,
    },
    abhaId: {
      type: String,
      default: null,
    },
    abhaNumber: {
      type: String,
      default: null,
    },
    abhaAddress: {
      type: String,
      default: null,
    },
    abhaCardUrl: {
      type: String,
      default: null,
    },
    gender: {
      type: String,
      required: false,
    },
    address: {
      line: { type: String, default: null },
      districtName: { type: String, default: null },
      stateName: { type: String, default: null },
      pinCode: { type: String, default: null },
      districtCode: { type: String, default: null },
      stateCode: { type: String, default: null },
      townName: { type: String, default: null }
    },
    kycStatus: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

const User = mongoose.model("User", userSchema);
const FamilyMember = mongoose.model("FamilyMember", familyMemberSchema);

module.exports = { User, FamilyMember };

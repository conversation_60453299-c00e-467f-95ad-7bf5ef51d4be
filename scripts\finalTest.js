/**
 * Final comprehensive test to verify the email duplicate key fix
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { User } = require('../models/user.model');

async function runFinalTest() {
  try {
    console.log('🧪 Running final comprehensive test...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');
    
    // Test 1: Create multiple users with mobile numbers (the original issue)
    console.log('\n📱 Test 1: Creating multiple users with mobile numbers...');
    const testMobiles = ['9834033728', '9876543210', '9123456789'];
    const createdUsers = [];
    
    for (const mobile of testMobiles) {
      try {
        // Clean up any existing user first
        await User.deleteOne({ mobile });
        
        const user = await User.create({
          mobile,
          profiles: [],
          abhaAddresses: []
        });
        createdUsers.push(user._id);
        console.log(`✅ Created user with mobile ${mobile} (ID: ${user._id})`);
        console.log(`   Email field present: ${user.email !== undefined ? 'Yes' : 'No'}`);
      } catch (error) {
        console.error(`❌ Failed to create user with mobile ${mobile}:`, error.message);
      }
    }
    
    // Test 2: Try to create duplicate mobile (should fail)
    console.log('\n🔄 Test 2: Testing duplicate mobile rejection...');
    try {
      await User.create({
        mobile: '9834033728', // Same as first test mobile
        profiles: [],
        abhaAddresses: []
      });
      console.log('❌ Unexpected: Duplicate mobile was allowed');
    } catch (error) {
      if (error.code === 11000 && error.message.includes('mobile')) {
        console.log('✅ Expected: Duplicate mobile correctly rejected');
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }
    
    // Test 3: Create users with emails
    console.log('\n📧 Test 3: Creating users with emails...');
    const testEmails = ['<EMAIL>', '<EMAIL>'];
    
    for (const email of testEmails) {
      try {
        // Clean up any existing user first
        await User.deleteOne({ email });
        
        const user = await User.create({
          email,
          profiles: []
        });
        createdUsers.push(user._id);
        console.log(`✅ Created user with email ${email} (ID: ${user._id})`);
      } catch (error) {
        console.error(`❌ Failed to create user with email ${email}:`, error.message);
      }
    }
    
    // Test 4: Try to create duplicate email (should fail)
    console.log('\n🔄 Test 4: Testing duplicate email rejection...');
    try {
      await User.create({
        email: '<EMAIL>', // Same as first test email
        profiles: []
      });
      console.log('❌ Unexpected: Duplicate email was allowed');
    } catch (error) {
      if (error.code === 11000 && error.message.includes('email')) {
        console.log('✅ Expected: Duplicate email correctly rejected');
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }
    
    // Test 5: Create user with both mobile and email
    console.log('\n📱📧 Test 5: Creating user with both mobile and email...');
    try {
      await User.deleteOne({ mobile: '9999999999' });
      await User.deleteOne({ email: '<EMAIL>' });
      
      const user = await User.create({
        mobile: '9999999999',
        email: '<EMAIL>',
        profiles: []
      });
      createdUsers.push(user._id);
      console.log(`✅ Created user with both mobile and email (ID: ${user._id})`);
    } catch (error) {
      console.error(`❌ Failed to create user with both mobile and email:`, error.message);
    }
    
    // Test 6: Simulate the original error scenario
    console.log('\n🎯 Test 6: Simulating original error scenario...');
    const originalMobile = '9834033728';
    
    // This should work now (creating a user with just mobile, no email field)
    try {
      // Clean up first
      await User.deleteOne({ mobile: originalMobile });
      
      const user = await User.create({
        mobile: originalMobile,
        profiles: [],
        abhaAddresses: []
      });
      
      console.log('✅ Original error scenario now works!');
      console.log(`   User created with mobile: ${user.mobile}`);
      console.log(`   Email field exists: ${user.email !== undefined ? 'Yes' : 'No'}`);
      console.log(`   Email value: ${user.email}`);
      
      createdUsers.push(user._id);
      
    } catch (error) {
      if (error.code === 11000 && error.message.includes('email')) {
        console.error('❌ CRITICAL: Original email duplicate key error still occurs!');
        console.error('Error:', error.message);
      } else {
        console.error('❌ Unexpected error in original scenario:', error.message);
      }
    }
    
    // Clean up all test users
    console.log('\n🧹 Cleaning up test users...');
    for (const userId of createdUsers) {
      await User.deleteOne({ _id: userId });
    }
    console.log(`Cleaned up ${createdUsers.length} test users`);
    
    console.log('\n🎉 Final test completed!');
    console.log('✅ The duplicate key error on email field has been resolved.');
    console.log('✅ Users can now be created with mobile numbers without email conflicts.');
    
  } catch (error) {
    console.error('❌ Final test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

runFinalTest();

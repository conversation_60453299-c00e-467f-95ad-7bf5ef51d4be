/**
 * Migration script to fix email index issue
 * 
 * This script:
 * 1. Drops the existing unique email index that doesn't allow multiple empty strings
 * 2. Updates all users with empty string emails to null
 * 3. Creates a new sparse unique index on email field
 * 
 * Usage:
 * node scripts/fixEmailIndexMigration.js
 */

require('dotenv').config();
const mongoose = require('mongoose');

async function runMigration() {
  try {
    console.log('Starting email index migration...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to MongoDB');
    
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    // Step 1: Check existing indexes
    console.log('\nStep 1: Checking existing indexes...');
    const indexes = await usersCollection.indexes();
    console.log('Current indexes:', indexes.map(idx => ({ name: idx.name, key: idx.key })));
    
    // Step 2: Drop the existing email index if it exists
    console.log('\nStep 2: Dropping existing email index...');
    try {
      await usersCollection.dropIndex('email_1');
      console.log('Successfully dropped email_1 index');
    } catch (error) {
      if (error.code === 27) {
        console.log('email_1 index does not exist, skipping drop');
      } else {
        console.error('Error dropping email_1 index:', error.message);
      }
    }
    
    // Step 3: Update all users with empty string emails to null
    console.log('\nStep 3: Updating users with empty string emails to null...');
    const updateResult = await usersCollection.updateMany(
      { email: "" },
      { $set: { email: null } }
    );
    console.log(`Updated ${updateResult.modifiedCount} users with empty email strings`);
    
    // Step 4: Create new sparse unique index on email
    console.log('\nStep 4: Creating new sparse unique index on email...');
    try {
      await usersCollection.createIndex(
        { email: 1 }, 
        { 
          unique: true, 
          sparse: true,
          name: 'email_1_sparse'
        }
      );
      console.log('Successfully created sparse unique index on email');
    } catch (error) {
      console.error('Error creating sparse unique index:', error.message);
    }
    
    // Step 5: Verify the new index
    console.log('\nStep 5: Verifying new indexes...');
    const newIndexes = await usersCollection.indexes();
    console.log('Updated indexes:', newIndexes.map(idx => ({ 
      name: idx.name, 
      key: idx.key, 
      unique: idx.unique,
      sparse: idx.sparse 
    })));
    
    // Step 6: Test the fix by checking for duplicate null emails
    console.log('\nStep 6: Checking for users with null emails...');
    const nullEmailCount = await usersCollection.countDocuments({ email: null });
    console.log(`Found ${nullEmailCount} users with null emails (this is expected and OK)`);
    
    const emptyEmailCount = await usersCollection.countDocuments({ email: "" });
    console.log(`Found ${emptyEmailCount} users with empty string emails (should be 0)`);
    
    console.log('\n✅ Migration completed successfully!');
    console.log('The email field now allows multiple null values but ensures unique non-null emails.');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the migration
runMigration();

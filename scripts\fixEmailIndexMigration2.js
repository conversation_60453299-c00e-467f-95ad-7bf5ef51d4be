/**
 * Enhanced migration script to fix email index issue
 * 
 * This script:
 * 1. Drops ALL existing email indexes
 * 2. Updates all users with empty string or null emails to undefined (removes the field)
 * 3. Creates a proper sparse unique index on email field
 * 
 * Usage:
 * node scripts/fixEmailIndexMigration2.js
 */

require('dotenv').config();
const mongoose = require('mongoose');

async function runEnhancedMigration() {
  try {
    console.log('Starting enhanced email index migration...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to MongoDB');
    
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    // Step 1: Check existing indexes
    console.log('\nStep 1: Checking existing indexes...');
    const indexes = await usersCollection.indexes();
    console.log('Current indexes:', indexes.map(idx => ({ 
      name: idx.name, 
      key: idx.key,
      unique: idx.unique,
      sparse: idx.sparse 
    })));
    
    // Step 2: Drop ALL email-related indexes
    console.log('\nStep 2: Dropping all email-related indexes...');
    const emailIndexes = indexes.filter(idx => 
      idx.name.includes('email') || 
      (idx.key && idx.key.email)
    );
    
    for (const index of emailIndexes) {
      try {
        await usersCollection.dropIndex(index.name);
        console.log(`Successfully dropped index: ${index.name}`);
      } catch (error) {
        console.error(`Error dropping index ${index.name}:`, error.message);
      }
    }
    
    // Step 3: Remove email field entirely from users who don't have a real email
    console.log('\nStep 3: Removing email field from users with empty/null emails...');
    
    // First, update empty strings to null
    const emptyStringResult = await usersCollection.updateMany(
      { email: "" },
      { $unset: { email: "" } }
    );
    console.log(`Removed email field from ${emptyStringResult.modifiedCount} users with empty strings`);
    
    // Then, remove null emails
    const nullResult = await usersCollection.updateMany(
      { email: null },
      { $unset: { email: "" } }
    );
    console.log(`Removed email field from ${nullResult.modifiedCount} users with null emails`);
    
    // Step 4: Create new sparse unique index on email
    console.log('\nStep 4: Creating new sparse unique index on email...');
    try {
      await usersCollection.createIndex(
        { email: 1 }, 
        { 
          unique: true, 
          sparse: true,
          name: 'email_unique_sparse',
          background: true
        }
      );
      console.log('Successfully created sparse unique index on email');
    } catch (error) {
      console.error('Error creating sparse unique index:', error.message);
    }
    
    // Step 5: Verify the new index
    console.log('\nStep 5: Verifying new indexes...');
    const newIndexes = await usersCollection.indexes();
    console.log('Updated indexes:', newIndexes.map(idx => ({ 
      name: idx.name, 
      key: idx.key, 
      unique: idx.unique,
      sparse: idx.sparse 
    })));
    
    // Step 6: Check the current state
    console.log('\nStep 6: Checking current user email states...');
    const totalUsers = await usersCollection.countDocuments();
    const usersWithEmail = await usersCollection.countDocuments({ 
      email: { $exists: true, $ne: null, $ne: "" } 
    });
    const usersWithoutEmail = await usersCollection.countDocuments({ 
      email: { $exists: false } 
    });
    
    console.log(`Total users: ${totalUsers}`);
    console.log(`Users with email field: ${usersWithEmail}`);
    console.log(`Users without email field: ${usersWithoutEmail}`);
    
    console.log('\n✅ Enhanced migration completed successfully!');
    console.log('Users without emails now have no email field at all, allowing unlimited mobile-only users.');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the migration
runEnhancedMigration();

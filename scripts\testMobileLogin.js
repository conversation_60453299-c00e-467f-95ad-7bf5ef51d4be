/**
 * Simple test to verify mobile login works after the fix
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { User } = require('../models/user.model');

async function testMobileLogin() {
  try {
    console.log('Testing mobile user creation...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to MongoDB');
    
    const testMobile = '9834033728';
    
    // First, clean up any existing test user
    await User.deleteOne({ mobile: testMobile });
    console.log('Cleaned up any existing test user');
    
    // Try to create a user with mobile (this was failing before)
    console.log(`Creating user with mobile: ${testMobile}`);
    
    const user = await User.create({
      mobile: testMobile,
      profiles: [],
      abhaAddresses: []
    });
    
    console.log('✅ SUCCESS: User created successfully!');
    console.log(`User ID: ${user._id}`);
    console.log(`Mobile: ${user.mobile}`);
    console.log(`Email: ${user.email === null ? 'null' : user.email}`);
    
    // Try to create another user with a different mobile
    const user2 = await User.create({
      mobile: '9876543210',
      profiles: [],
      abhaAddresses: []
    });
    
    console.log('✅ SUCCESS: Second user also created successfully!');
    console.log(`User 2 ID: ${user2._id}`);
    console.log(`User 2 Email: ${user2.email === null ? 'null' : user2.email}`);
    
    // Clean up
    await User.deleteOne({ _id: user._id });
    await User.deleteOne({ _id: user2._id });
    console.log('Cleaned up test users');
    
    console.log('\n🎉 All tests passed! The duplicate key error is fixed.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.code === 11000) {
      console.error('This is still the duplicate key error - the fix may not have worked properly');
    }
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

testMobileLogin();

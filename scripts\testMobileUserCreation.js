/**
 * Test script to verify mobile user creation works after email index fix
 * 
 * This script creates multiple test users with mobile numbers to ensure
 * the duplicate key error is resolved.
 * 
 * Usage:
 * node scripts/testMobileUserCreation.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { User } = require('../models/user.model');

async function testMobileUserCreation() {
  try {
    console.log('Starting mobile user creation test...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to MongoDB');
    
    // Test mobile numbers
    const testMobiles = [
      '9834033728', // The mobile from the error
      '9876543210',
      '9123456789',
      '9999999999'
    ];
    
    console.log('\nCreating test users with mobile numbers...');
    
    for (const mobile of testMobiles) {
      try {
        // Check if user already exists
        const existingUser = await User.findOne({ mobile });
        if (existingUser) {
          console.log(`User with mobile ${mobile} already exists, skipping...`);
          continue;
        }
        
        // Create new user
        const user = await User.create({
          mobile,
          profiles: [],
          abhaAddresses: []
        });
        
        console.log(`✅ Successfully created user with mobile ${mobile}, ID: ${user._id}`);
        console.log(`   Email field value: ${user.email === null ? 'null' : `"${user.email}"`}`);
        
      } catch (error) {
        console.error(`❌ Failed to create user with mobile ${mobile}:`, error.message);
      }
    }
    
    // Test creating a user with an actual email
    console.log('\nTesting user creation with email...');
    try {
      const emailUser = await User.create({
        email: '<EMAIL>',
        profiles: []
      });
      console.log(`✅ Successfully created user with email, ID: ${emailUser._id}`);
    } catch (error) {
      console.error(`❌ Failed to create user with email:`, error.message);
    }
    
    // Test creating another user with the same email (should fail)
    console.log('\nTesting duplicate email creation (should fail)...');
    try {
      await User.create({
        email: '<EMAIL>',
        profiles: []
      });
      console.log(`❌ Unexpected: Created duplicate email user (this should have failed)`);
    } catch (error) {
      console.log(`✅ Expected: Duplicate email creation failed: ${error.message}`);
    }
    
    console.log('\n📊 Final statistics:');
    const totalUsers = await User.countDocuments();
    const usersWithMobile = await User.countDocuments({ mobile: { $exists: true, $ne: null } });
    const usersWithEmail = await User.countDocuments({ email: { $exists: true, $ne: null } });
    const usersWithNullEmail = await User.countDocuments({ email: null });
    
    console.log(`Total users: ${totalUsers}`);
    console.log(`Users with mobile: ${usersWithMobile}`);
    console.log(`Users with email: ${usersWithEmail}`);
    console.log(`Users with null email: ${usersWithNullEmail}`);
    
    console.log('\n✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the test
testMobileUserCreation();

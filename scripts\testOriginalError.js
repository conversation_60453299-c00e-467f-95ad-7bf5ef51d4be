/**
 * Test the exact scenario from the original error
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { User } = require('../models/user.model');

async function testOriginalError() {
  try {
    console.log('Testing the exact scenario from the original error...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to MongoDB');
    
    const originalMobile = '9834033728'; // From the error message
    
    console.log(`Testing with original mobile number: ${originalMobile}`);
    
    // Try to create multiple users with this mobile (should fail on mobile uniqueness, not email)
    try {
      const user1 = await User.create({
        mobile: originalMobile,
        profiles: [],
        abhaAddresses: []
      });
      console.log('✅ First user created successfully');
      console.log(`User ID: ${user1._id}`);
      console.log(`Email field exists: ${user1.email !== undefined}`);
      console.log(`Email value: ${user1.email}`);
      
      // Try to create another user with the same mobile (should fail on mobile uniqueness)
      try {
        await User.create({
          mobile: originalMobile,
          profiles: [],
          abhaAddresses: []
        });
        console.log('❌ Unexpected: Second user with same mobile was created');
      } catch (error) {
        if (error.code === 11000 && error.message.includes('mobile')) {
          console.log('✅ Expected: Duplicate mobile rejected correctly');
        } else {
          console.log('❌ Unexpected error:', error.message);
        }
      }
      
      // Clean up
      await User.deleteOne({ _id: user1._id });
      console.log('Cleaned up test user');
      
    } catch (error) {
      if (error.code === 11000) {
        if (error.message.includes('email')) {
          console.error('❌ STILL FAILING: Email duplicate key error occurred');
          console.error('Error:', error.message);
        } else if (error.message.includes('mobile')) {
          console.log('✅ Expected: Mobile duplicate key error (this is correct behavior)');
        } else {
          console.error('❌ Unexpected duplicate key error:', error.message);
        }
      } else {
        console.error('❌ Unexpected error:', error.message);
      }
    }
    
    // Test creating users with different mobiles (should all work)
    console.log('\nTesting multiple users with different mobiles...');
    const testMobiles = ['9111111111', '9222222222', '9333333333'];
    const createdUsers = [];
    
    for (const mobile of testMobiles) {
      try {
        const user = await User.create({
          mobile,
          profiles: [],
          abhaAddresses: []
        });
        createdUsers.push(user._id);
        console.log(`✅ Created user with mobile ${mobile}`);
      } catch (error) {
        console.error(`❌ Failed to create user with mobile ${mobile}:`, error.message);
      }
    }
    
    // Clean up
    for (const userId of createdUsers) {
      await User.deleteOne({ _id: userId });
    }
    console.log('Cleaned up all test users');
    
    console.log('\n🎉 Original error scenario test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

testOriginalError();
